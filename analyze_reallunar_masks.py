#!/usr/bin/env python3
"""
RealLunar灰度标注图像分析工具

分析RealLunar数据集中的灰度标注图像，确定颜色值与类别的对应关系
"""

import os
import numpy as np
from PIL import Image
from collections import Counter


def analyze_mask_colors(dataset_path):
    """分析RealLunar数据集中的灰度标注图像颜色"""
    
    print("RealLunar灰度标注图像颜色分析")
    print("=" * 60)
    
    # 获取所有灰度标注图像
    mask_files = []
    for filename in os.listdir(dataset_path):
        if filename.startswith('g_') and filename.endswith('.png'):
            mask_files.append(os.path.join(dataset_path, filename))
    
    print(f"找到 {len(mask_files)} 个灰度标注图像")
    
    # 分析所有颜色
    all_colors = Counter()
    color_pixel_counts = Counter()
    
    for mask_file in mask_files[:10]:  # 分析前10个文件
        print(f"\n分析文件: {os.path.basename(mask_file)}")
        
        # 加载图像
        img = Image.open(mask_file)
        img_array = np.array(img)
        
        print(f"  图像模式: {img.mode}, 尺寸: {img.size}")
        
        # 获取唯一颜色
        if len(img_array.shape) == 3:  # RGBA图像
            pixels = img_array.reshape(-1, img_array.shape[2])
            unique_pixels, counts = np.unique(pixels, axis=0, return_counts=True)
            
            print(f"  唯一颜色数: {len(unique_pixels)}")
            for color, count in zip(unique_pixels, counts):
                color_tuple = tuple(color)
                all_colors[color_tuple] += 1
                color_pixel_counts[color_tuple] += count
                
                # 只显示主要颜色（像素数 > 100）
                if count > 100:
                    print(f"    {color}: {count:,} 像素")
    
    print(f"\n" + "=" * 60)
    print("全局颜色统计（按像素数排序）:")
    print("=" * 60)
    
    # 按像素总数排序
    sorted_colors = sorted(color_pixel_counts.items(), key=lambda x: x[1], reverse=True)
    
    for i, (color, total_pixels) in enumerate(sorted_colors):
        appear_count = all_colors[color]
        print(f"{i+1:2d}. {color}: {total_pixels:,} 总像素, 出现在 {appear_count} 个图像中")
        
        # 只显示前20个最常见的颜色
        if i >= 19:
            break
    
    return sorted_colors


def analyze_color_mapping(dataset_path):
    """分析颜色与类别的对应关系"""
    
    print(f"\n" + "=" * 60)
    print("颜色与类别对应关系分析:")
    print("=" * 60)
    
    # 基于观察到的颜色模式进行分析
    color_analysis = {
        (0, 0, 0, 255): "背景 (Background) - 黑色",
        (0, 255, 0, 255): "绿色对象 (Green Objects) - 小型岩石/特征",
        (0, 0, 255, 255): "蓝色对象 (Blue Objects) - 大型岩石/特征", 
        (255, 0, 0, 255): "红色对象 (Red Objects) - 可能是特殊标记",
        (255, 0, 255, 255): "紫色对象 (Magenta Objects) - 可能是边界或特殊区域"
    }
    
    print("推测的颜色映射:")
    for color, description in color_analysis.items():
        print(f"  {color} -> {description}")
    
    return color_analysis


def generate_grayscale_mapping():
    """生成灰度值映射建议"""
    
    print(f"\n" + "=" * 60)
    print("建议的灰度值映射:")
    print("=" * 60)
    
    # 建议的灰度值映射
    grayscale_mapping = {
        0: "背景 (Background)",
        1: "绿色对象 (Green Objects/Small Rocks)", 
        2: "蓝色对象 (Blue Objects/Large Rocks)",
        3: "红色对象 (Red Objects/Special Features)",
        4: "紫色对象 (Magenta Objects/Boundaries)"
    }
    
    print("推荐的灰度值分配:")
    for gray_val, description in grayscale_mapping.items():
        print(f"  灰度值 {gray_val} -> {description}")
    
    print(f"\n对应的RGBA到灰度转换规则:")
    rgba_to_gray = {
        (0, 0, 0, 255): 0,      # 黑色 -> 背景
        (0, 255, 0, 255): 1,    # 绿色 -> 小型对象
        (0, 0, 255, 255): 2,    # 蓝色 -> 大型对象
        (255, 0, 0, 255): 3,    # 红色 -> 特殊特征
        (255, 0, 255, 255): 4,  # 紫色 -> 边界
    }
    
    for rgba, gray in rgba_to_gray.items():
        print(f"  RGBA {rgba} -> 灰度值 {gray}")
    
    return grayscale_mapping, rgba_to_gray


def create_conversion_script(dataset_path, rgba_to_gray_mapping):
    """创建RGBA到灰度转换的示例代码"""
    
    conversion_code = f'''
def convert_rgba_to_grayscale_mask(rgba_mask_path, output_path=None):
    """
    将RGBA标注图像转换为灰度标注图像
    
    Args:
        rgba_mask_path: RGBA标注图像路径
        output_path: 输出灰度图像路径（可选）
    
    Returns:
        numpy array: 灰度标注数组
    """
    from PIL import Image
    import numpy as np
    
    # 颜色映射表
    rgba_to_gray = {rgba_to_gray_mapping}
    
    # 加载RGBA图像
    rgba_img = Image.open(rgba_mask_path)
    rgba_array = np.array(rgba_img)
    
    # 创建灰度输出数组
    height, width = rgba_array.shape[:2]
    gray_array = np.zeros((height, width), dtype=np.uint8)
    
    # 转换每个像素
    for rgba_color, gray_value in rgba_to_gray.items():
        # 找到匹配的像素
        mask = np.all(rgba_array == rgba_color, axis=2)
        gray_array[mask] = gray_value
    
    # 保存灰度图像（可选）
    if output_path:
        gray_img = Image.fromarray(gray_array, mode='L')
        gray_img.save(output_path)
        print(f"灰度标注图像已保存到: {{output_path}}")
    
    return gray_array

# 使用示例:
# gray_mask = convert_rgba_to_grayscale_mask('datasets/data/RealLunar/g_PCAM1.png', 'output_gray_mask.png')
'''
    
    print(f"\n" + "=" * 60)
    print("RGBA到灰度转换代码:")
    print("=" * 60)
    print(conversion_code)
    
    return conversion_code


def main():
    """主函数"""
    dataset_path = 'datasets/data/RealLunar'
    
    if not os.path.exists(dataset_path):
        print(f"错误: 数据集路径不存在: {dataset_path}")
        return
    
    # 1. 分析颜色分布
    sorted_colors = analyze_mask_colors(dataset_path)
    
    # 2. 分析颜色映射
    color_mapping = analyze_color_mapping(dataset_path)
    
    # 3. 生成灰度映射建议
    grayscale_mapping, rgba_to_gray = generate_grayscale_mapping()
    
    # 4. 创建转换脚本
    conversion_code = create_conversion_script(dataset_path, rgba_to_gray)
    
    print(f"\n" + "=" * 60)
    print("总结:")
    print("=" * 60)
    print("1. RealLunar数据集当前使用RGBA颜色标注")
    print("2. 主要颜色包括: 黑色(背景)、绿色(小对象)、蓝色(大对象)、红色和紫色(特殊标记)")
    print("3. 建议使用0-4的灰度值进行重新标注")
    print("4. 可以使用上述转换代码将现有RGBA标注转换为灰度标注")


if __name__ == '__main__':
    main()
