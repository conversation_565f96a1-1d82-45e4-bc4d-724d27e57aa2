#!/usr/bin/env python3
"""
RealLunar数据集RGBA到灰度标注转换工具

将RealLunar数据集中的RGBA颜色标注图像转换为灰度标注图像，
便于语义分割模型训练和评估。

颜色映射规则:
- RGBA (0, 0, 0, 255) -> 灰度值 0 (背景)
- RGBA (0, 255, 0, 255) -> 灰度值 1 (绿色对象/小型岩石)
- RGBA (0, 0, 255, 255) -> 灰度值 2 (蓝色对象/大型岩石)
- RGBA (255, 0, 0, 255) -> 灰度值 3 (红色对象/特殊特征)
- RGBA (255, 0, 255, 255) -> 灰度值 4 (紫色对象/边界)
"""

import os
import numpy as np
from PIL import Image
from tqdm import tqdm
import argparse


class RealLunarConverter:
    """RealLunar数据集RGBA到灰度转换器"""
    
    def __init__(self):
        # RGBA到灰度值的映射表
        self.rgba_to_gray = {
            (0, 0, 0, 255): 0,      # 黑色 -> 背景
            (0, 255, 0, 255): 1,    # 绿色 -> 小型对象/岩石
            (0, 0, 255, 255): 2,    # 蓝色 -> 大型对象/岩石
            (255, 0, 0, 255): 3,    # 红色 -> 特殊特征
            (255, 0, 255, 255): 4,  # 紫色 -> 边界
        }
        
        # RGB到灰度值的映射表（处理没有Alpha通道的图像）
        self.rgb_to_gray = {
            (0, 0, 0): 0,      # 黑色 -> 背景
            (0, 255, 0): 1,    # 绿色 -> 小型对象/岩石
            (0, 0, 255): 2,    # 蓝色 -> 大型对象/岩石
            (255, 0, 0): 3,    # 红色 -> 特殊特征
            (255, 0, 255): 4,  # 紫色 -> 边界
        }
        
        # 类别信息
        self.class_names = [
            "Background",
            "Green_Objects",  # 小型岩石/特征
            "Blue_Objects",   # 大型岩石/特征
            "Red_Objects",    # 特殊特征
            "Magenta_Objects" # 边界/特殊区域
        ]
        
        self.num_classes = len(self.class_names)
    
    def convert_single_mask(self, rgba_mask_path, output_path=None, verbose=False):
        """
        转换单个RGBA标注图像为灰度标注图像
        
        Args:
            rgba_mask_path (str): RGBA标注图像路径
            output_path (str): 输出灰度图像路径（可选）
            verbose (bool): 是否显示详细信息
            
        Returns:
            numpy.ndarray: 灰度标注数组
        """
        if not os.path.exists(rgba_mask_path):
            raise FileNotFoundError(f"文件不存在: {rgba_mask_path}")
        
        # 加载RGBA图像
        rgba_img = Image.open(rgba_mask_path)
        rgba_array = np.array(rgba_img)
        
        if verbose:
            print(f"处理文件: {os.path.basename(rgba_mask_path)}")
            print(f"  图像模式: {rgba_img.mode}, 尺寸: {rgba_img.size}")
            print(f"  数组形状: {rgba_array.shape}")
        
        # 创建灰度输出数组
        height, width = rgba_array.shape[:2]
        gray_array = np.zeros((height, width), dtype=np.uint8)
        
        # 处理不同的图像模式
        if len(rgba_array.shape) == 3:
            if rgba_array.shape[2] == 4:  # RGBA
                color_mapping = self.rgba_to_gray
                # 转换每个像素
                for rgba_color, gray_value in color_mapping.items():
                    mask = np.all(rgba_array == rgba_color, axis=2)
                    gray_array[mask] = gray_value
                    
                    if verbose and np.any(mask):
                        pixel_count = np.sum(mask)
                        print(f"    {rgba_color} -> {gray_value}: {pixel_count} 像素")
                        
            elif rgba_array.shape[2] == 3:  # RGB
                color_mapping = self.rgb_to_gray
                # 转换每个像素
                for rgb_color, gray_value in color_mapping.items():
                    mask = np.all(rgba_array == rgb_color, axis=2)
                    gray_array[mask] = gray_value
                    
                    if verbose and np.any(mask):
                        pixel_count = np.sum(mask)
                        print(f"    {rgb_color} -> {gray_value}: {pixel_count} 像素")
        
        # 检查未映射的像素
        unmapped_pixels = np.sum(gray_array == 0) - np.sum(np.all(rgba_array == (0, 0, 0, 255), axis=2) if rgba_array.shape[2] == 4 else np.all(rgba_array == (0, 0, 0), axis=2))
        if unmapped_pixels > 0 and verbose:
            print(f"    警告: {unmapped_pixels} 个像素未能映射")
        
        # 保存灰度图像
        if output_path:
            gray_img = Image.fromarray(gray_array, mode='L')
            gray_img.save(output_path)
            if verbose:
                print(f"    灰度标注图像已保存到: {output_path}")
        
        return gray_array
    
    def convert_dataset(self, dataset_path, output_dir=None, prefix='gray_', verbose=True):
        """
        转换整个RealLunar数据集
        
        Args:
            dataset_path (str): RealLunar数据集路径
            output_dir (str): 输出目录（可选，默认为原目录）
            prefix (str): 输出文件前缀
            verbose (bool): 是否显示进度
            
        Returns:
            dict: 转换统计信息
        """
        if not os.path.exists(dataset_path):
            raise FileNotFoundError(f"数据集路径不存在: {dataset_path}")
        
        # 设置输出目录
        if output_dir is None:
            output_dir = dataset_path
        else:
            os.makedirs(output_dir, exist_ok=True)
        
        # 查找所有RGBA标注图像
        mask_files = []
        for filename in os.listdir(dataset_path):
            if filename.startswith('g_') and filename.endswith('.png'):
                mask_files.append(filename)
        
        if not mask_files:
            print(f"在 {dataset_path} 中未找到RGBA标注图像（g_*.png）")
            return {}
        
        print(f"找到 {len(mask_files)} 个RGBA标注图像")
        print(f"输出目录: {output_dir}")
        print(f"输出前缀: {prefix}")
        
        # 转换统计
        stats = {
            'total_files': len(mask_files),
            'converted_files': 0,
            'failed_files': 0,
            'class_pixel_counts': {i: 0 for i in range(self.num_classes)}
        }
        
        # 转换每个文件
        for filename in tqdm(mask_files, desc="转换标注图像"):
            try:
                input_path = os.path.join(dataset_path, filename)
                
                # 生成输出文件名
                base_name = filename[2:]  # 移除 'g_' 前缀
                output_filename = f"{prefix}{base_name}"
                output_path = os.path.join(output_dir, output_filename)
                
                # 转换图像
                gray_array = self.convert_single_mask(
                    input_path, 
                    output_path, 
                    verbose=False
                )
                
                # 统计类别像素数
                for class_id in range(self.num_classes):
                    pixel_count = np.sum(gray_array == class_id)
                    stats['class_pixel_counts'][class_id] += pixel_count
                
                stats['converted_files'] += 1
                
            except Exception as e:
                print(f"转换失败 {filename}: {e}")
                stats['failed_files'] += 1
        
        # 打印统计信息
        print(f"\n转换完成!")
        print(f"成功转换: {stats['converted_files']} 个文件")
        print(f"转换失败: {stats['failed_files']} 个文件")
        
        print(f"\n类别像素统计:")
        for class_id, pixel_count in stats['class_pixel_counts'].items():
            class_name = self.class_names[class_id]
            percentage = pixel_count / sum(stats['class_pixel_counts'].values()) * 100
            print(f"  类别 {class_id} ({class_name}): {pixel_count:,} 像素 ({percentage:.2f}%)")
        
        return stats
    
    def verify_conversion(self, original_path, converted_path):
        """
        验证转换结果
        
        Args:
            original_path (str): 原始RGBA图像路径
            converted_path (str): 转换后的灰度图像路径
            
        Returns:
            dict: 验证结果
        """
        # 加载图像
        original_img = Image.open(original_path)
        converted_img = Image.open(converted_path)
        
        original_array = np.array(original_img)
        converted_array = np.array(converted_img)
        
        # 验证尺寸
        size_match = original_array.shape[:2] == converted_array.shape
        
        # 验证类别数
        unique_values = np.unique(converted_array)
        valid_classes = all(val in range(self.num_classes) for val in unique_values)
        
        # 验证像素映射
        mapping_correct = True
        if len(original_array.shape) == 3 and original_array.shape[2] == 4:
            for rgba_color, gray_value in self.rgba_to_gray.items():
                rgba_mask = np.all(original_array == rgba_color, axis=2)
                gray_mask = converted_array == gray_value
                if not np.array_equal(rgba_mask, gray_mask):
                    mapping_correct = False
                    break
        
        return {
            'size_match': size_match,
            'valid_classes': valid_classes,
            'mapping_correct': mapping_correct,
            'unique_values': unique_values.tolist(),
            'original_shape': original_array.shape,
            'converted_shape': converted_array.shape
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RealLunar数据集RGBA到灰度标注转换工具')
    parser.add_argument('--dataset_path', type=str, default='datasets/data/RealLunar',
                        help='RealLunar数据集路径')
    parser.add_argument('--output_dir', type=str, default=None,
                        help='输出目录（默认为原目录）')
    parser.add_argument('--prefix', type=str, default='gray_',
                        help='输出文件前缀')
    parser.add_argument('--single_file', type=str, default=None,
                        help='转换单个文件')
    parser.add_argument('--verify', type=str, nargs=2, default=None,
                        help='验证转换结果 [原始文件] [转换文件]')
    parser.add_argument('--verbose', action='store_true',
                        help='显示详细信息')
    
    args = parser.parse_args()
    
    converter = RealLunarConverter()
    
    if args.verify:
        # 验证转换结果
        original_path, converted_path = args.verify
        result = converter.verify_conversion(original_path, converted_path)
        print("验证结果:")
        for key, value in result.items():
            print(f"  {key}: {value}")
    
    elif args.single_file:
        # 转换单个文件
        output_path = args.single_file.replace('.png', '_gray.png')
        gray_array = converter.convert_single_mask(
            args.single_file, 
            output_path, 
            verbose=args.verbose
        )
        print(f"转换完成: {output_path}")
        print(f"唯一灰度值: {np.unique(gray_array)}")
    
    else:
        # 转换整个数据集
        stats = converter.convert_dataset(
            args.dataset_path,
            args.output_dir,
            args.prefix,
            args.verbose
        )


if __name__ == '__main__':
    main()
