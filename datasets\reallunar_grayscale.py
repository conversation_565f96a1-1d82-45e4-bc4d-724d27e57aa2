import os
import pandas as pd
import numpy as np
from PIL import Image, ImageDraw
import torch
from torch.utils import data
from datasets import utils


class RealLunarGrayscaleDataset(data.Dataset):
    """RealLunar Dataset with Grayscale Annotations for semantic segmentation
    
    A real lunar surface dataset containing terrain segmentation data.
    This version uses grayscale annotation masks instead of RGBA color masks.
    
    Classes (5-class mode):
        0: Background (lunar surface)
        1: Green objects (small rocks/features)
        2: Blue objects (large rocks/features)
        3: Red objects (special features)
        4: Magenta objects (boundaries/special areas)
        
    Classes (2-class mode with merge_rocks=True):
        0: Background (lunar surface)
        1: Rocks (merged green, blue, red, magenta objects)
    """
    
    def __init__(self, root, split='train', transform=None, camera_type='both', 
                 merge_rocks=False, use_grayscale_masks=True, grayscale_prefix='gray_'):
        """
        Args:
            root (str): Root directory of the dataset
            split (str): Dataset split ('train', 'val', 'test')
            transform: Data augmentation transforms
            camera_type (str): Camera type to use ('PCAM', 'TCAM', 'both')
            merge_rocks (bool): If True, merge all object classes into single 'Rocks' class (2 classes total)
                               If False, keep separate object classes (5 classes total)
            use_grayscale_masks (bool): If True, use grayscale annotation masks; if False, use RGBA masks
            grayscale_prefix (str): Prefix for grayscale mask files
        """
        self.root = os.path.expanduser(root)
        self.split = split
        self.transform = transform
        self.camera_type = camera_type
        self.merge_rocks = merge_rocks
        self.use_grayscale_masks = use_grayscale_masks
        self.grayscale_prefix = grayscale_prefix

        # Dataset path
        self.dataset_path = os.path.join(self.root, 'RealLunar')

        # Class information - depends on merge_rocks setting
        if merge_rocks:
            self.num_classes = 2  # Background + Rocks (merged)
            self.class_names = ['Background', 'Rocks']
            print("RealLunar Dataset Mode: 2-class (Background + Rocks) - Compatible with Jiayu/LunarSim evaluation")
        else:
            self.num_classes = 5  # Background + 4 object types
            self.class_names = ['Background', 'Green_Objects', 'Blue_Objects', 'Red_Objects', 'Magenta_Objects']
            print("RealLunar Dataset Mode: 5-class (Background + 4 object types) - Full grayscale mode")

        self.ignore_index = 255

        # Color mapping for visualization (RGB format)
        if merge_rocks:
            self.class_colors = [
                [0, 0, 0],      # Background - Black
                [255, 0, 0],    # Merged rocks - Red
            ]
            print(f"Color Detection: Background={self.class_colors[0]}, Rocks={self.class_colors[1]}")
        else:
            self.class_colors = [
                [0, 0, 0],      # Background - Black
                [0, 255, 0],    # Green objects - Green
                [0, 0, 255],    # Blue objects - Blue
                [255, 0, 0],    # Red objects - Red
                [255, 0, 255],  # Magenta objects - Magenta
            ]
            print(f"Color Detection: Background={self.class_colors[0]}, Green={self.class_colors[1]}, "
                  f"Blue={self.class_colors[2]}, Red={self.class_colors[3]}, Magenta={self.class_colors[4]}")

        # Get all available samples
        self.samples = self._get_samples()
        
        # Split dataset: 70% train, 20% val, 10% test
        self.train_samples, self.val_samples, self.test_samples = self._split_dataset()
        
        # Select samples based on split
        if split == 'train':
            self.current_samples = self.train_samples
        elif split == 'val':
            self.current_samples = self.val_samples
        elif split == 'test':
            self.current_samples = self.test_samples
        else:
            raise ValueError(f"Invalid split: {split}. Must be 'train', 'val', or 'test'")
            
        print(f"RealLunar Dataset - {split}: {len(self.current_samples)} samples")
        print(f"Using {'grayscale' if use_grayscale_masks else 'RGBA'} annotation masks")

    def _get_samples(self):
        """Get all available samples from the dataset directory"""
        samples = []
        
        # Load bounding box data (for RGBA mask generation if needed)
        pcam_csv = os.path.join(self.dataset_path, 'PCAM_bounding_boxes.csv')
        tcam_csv = os.path.join(self.dataset_path, 'TCAM_bounding_boxes.csv')
        
        pcam_boxes = pd.read_csv(pcam_csv) if os.path.exists(pcam_csv) else pd.DataFrame()
        tcam_boxes = pd.read_csv(tcam_csv) if os.path.exists(tcam_csv) else pd.DataFrame()
        
        # Process PCAM images
        if self.camera_type in ['PCAM', 'both'] and not pcam_boxes.empty:
            for frame_id in pcam_boxes['Frame'].unique():
                img_path = os.path.join(self.dataset_path, f'PCAM{frame_id}.png')
                
                # Determine mask path based on mode
                if self.use_grayscale_masks:
                    mask_path = os.path.join(self.dataset_path, f'{self.grayscale_prefix}PCAM{frame_id}.png')
                else:
                    mask_path = os.path.join(self.dataset_path, f'g_PCAM{frame_id}.png')
                
                if os.path.exists(img_path) and os.path.exists(mask_path):
                    frame_boxes = pcam_boxes[pcam_boxes['Frame'] == frame_id]
                    samples.append({
                        'camera': 'PCAM',
                        'frame_id': frame_id,
                        'image': img_path,
                        'mask': mask_path,
                        'boxes': frame_boxes
                    })
        
        # Process TCAM images
        if self.camera_type in ['TCAM', 'both'] and not tcam_boxes.empty:
            for frame_id in tcam_boxes['Frame'].unique():
                img_path = os.path.join(self.dataset_path, f'TCAM{frame_id}.png')
                
                # Determine mask path based on mode
                if self.use_grayscale_masks:
                    mask_path = os.path.join(self.dataset_path, f'{self.grayscale_prefix}TCAM{frame_id}.png')
                else:
                    mask_path = os.path.join(self.dataset_path, f'g_TCAM{frame_id}.png')
                
                if os.path.exists(img_path) and os.path.exists(mask_path):
                    frame_boxes = tcam_boxes[tcam_boxes['Frame'] == frame_id]
                    samples.append({
                        'camera': 'TCAM',
                        'frame_id': frame_id,
                        'image': img_path,
                        'mask': mask_path,
                        'boxes': frame_boxes
                    })
        
        # Sort by camera type and frame id
        samples.sort(key=lambda x: (x['camera'], x['frame_id']))
        return samples
    
    def _split_dataset(self):
        """Split dataset into train/val/test with 7:2:1 ratio"""
        total_samples = len(self.samples)
        
        # Calculate split indices
        train_end = int(0.7 * total_samples)
        val_end = int(0.9 * total_samples)
        
        train_samples = self.samples[:train_end]
        val_samples = self.samples[train_end:val_end]
        test_samples = self.samples[val_end:]
        
        print(f"Dataset split - Train: {len(train_samples)}, Val: {len(val_samples)}, Test: {len(test_samples)}")

        return train_samples, val_samples, test_samples
    
    def _load_grayscale_mask(self, mask_path):
        """Load and process grayscale annotation mask
        
        Args:
            mask_path: Path to grayscale mask file
            
        Returns:
            numpy array with class indices
        """
        mask_img = Image.open(mask_path)
        mask_array = np.array(mask_img)
        
        # Handle different image modes
        if len(mask_array.shape) == 3:
            # Convert to grayscale if needed
            mask_array = mask_array[:, :, 0]  # Take first channel
        
        # Apply class merging if needed
        if self.merge_rocks:
            # Merge all non-background classes into class 1
            merged_mask = np.zeros_like(mask_array)
            merged_mask[mask_array == 0] = 0  # Background stays 0
            merged_mask[mask_array > 0] = 1   # All objects become 1
            mask_array = merged_mask
        
        return mask_array
    
    def __getitem__(self, index):
        """Get a sample from the dataset"""
        sample = self.current_samples[index]
        
        # Load image and convert to RGB
        image = Image.open(sample['image']).convert('RGB')
        
        # Load mask
        if self.use_grayscale_masks:
            mask_array = self._load_grayscale_mask(sample['mask'])
        else:
            # Fallback to RGBA mask processing (original method)
            mask_array = self._create_mask_from_boxes(image.size, sample['boxes'])
        
        mask = Image.fromarray(mask_array.astype(np.uint8))
        
        # Apply transforms
        if self.transform is not None:
            image, mask = self.transform(image, mask)
        
        return image, mask
    
    def _create_mask_from_boxes(self, image_size, boxes_df):
        """Create semantic segmentation mask from bounding boxes (fallback method)"""
        width, height = image_size
        mask = np.zeros((height, width), dtype=np.uint8)
        
        # Create PIL Image for drawing
        mask_pil = Image.fromarray(mask)
        draw = ImageDraw.Draw(mask_pil)
        
        for _, box in boxes_df.iterrows():
            x1 = int(box['TopLeftCornerX'])
            y1 = int(box['TopLeftCornerY'])
            x2 = int(x1 + box['Length'])
            y2 = int(y1 + box['Height'])

            # Determine class based on color and merge_rocks setting
            if box['Color'].lower() in ['green', 'blue', 'red']:
                if self.merge_rocks:
                    class_id = 1  # All objects merged into class 1
                else:
                    if box['Color'].lower() == 'green':
                        class_id = 1  # Green objects
                    elif box['Color'].lower() == 'blue':
                        class_id = 2  # Blue objects
                    else:  # red
                        class_id = 3  # Red objects
            else:
                continue  # Skip unknown colors

            # Draw filled rectangle
            draw.rectangle([x1, y1, x2, y2], fill=class_id)
        
        return np.array(mask_pil)
    
    def __len__(self):
        return len(self.current_samples)
    
    def get_class_names(self):
        """Get class names"""
        return self.class_names
    
    def get_class_colors(self):
        """Get class colors for visualization (RGB format)"""
        return self.class_colors

    def decode_target(self, target):
        """Convert class indices to RGB colors for visualization"""
        # Handle invalid class indices
        target = np.clip(target, 0, len(self.class_colors) - 1)
        
        # Convert to RGB
        color_map = np.array(self.class_colors, dtype=np.uint8)
        rgb_target = color_map[target]
        
        return rgb_target


if __name__ == '__main__':
    # Test the dataset
    import matplotlib.pyplot as plt

    # Test dataset loading
    dataset_root = '../datasets/data'

    print("Testing RealLunar Grayscale Dataset...")

    for merge_rocks in [False, True]:
        mode_name = "2-class (merged rocks)" if merge_rocks else "5-class (full grayscale)"
        print(f"\n=== Testing {mode_name} mode ===")

        for split in ['train', 'val', 'test']:
            try:
                dataset = RealLunarGrayscaleDataset(
                    root=dataset_root, 
                    split=split, 
                    merge_rocks=merge_rocks,
                    use_grayscale_masks=True
                )
                print(f"\n{split.upper()} Dataset ({mode_name}):")
                print(f"  Samples: {len(dataset)}")
                print(f"  Classes: {dataset.num_classes}")
                print(f"  Class names: {dataset.get_class_names()}")

                if len(dataset) > 0:
                    # Test loading first sample
                    image, mask = dataset[0]
                    print(f"  Sample 0 - Image shape: {np.array(image).shape}, Mask shape: {np.array(mask).shape}")
                    print(f"  Mask unique values: {np.unique(np.array(mask))}")

            except Exception as e:
                print(f"Error loading {split} dataset: {e}")
